using PBC.UtilityService.Services;
using PBC.UtilityService.Utilities;
using Microsoft.OpenApi.Models;
using System.Reflection;
using Serilog;

Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Information()
    .WriteTo.File("Logs/errors-.log", rollingInterval: RollingInterval.Day, retainedFileCountLimit: 2)
    .CreateLogger();


var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Host.UseSerilog(); 
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
    {
        Title = "PBC Utility Service",
        Version = "v1",
        Description = "Contains all Utility functions for the PBC microservices architecture"
    });
    c.CustomSchemaIds(type => type.FullName);

    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    c.IncludeXmlComments(xmlPath);

});

// Add HTTP client for inter-service communication
builder.Services.AddHttpClient();

// Add health checks
builder.Services.AddHealthChecks();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});


builder.Services.AddControllers()
    .AddNewtonsoftJson(options =>
    {
        options.SerializerSettings.ContractResolver = new Newtonsoft.Json.Serialization.DefaultContractResolver();
    });

// Register application services
builder.Services.AddScoped<IHealthService, HealthService>();
builder.Services.AddScoped<IACLPropertiesService, ACLPropertiesService>();
builder.Services.AddScoped<IAdvanceFilterService, AdvanceFilterService>();
builder.Services.AddScoped<IBCryptService, BCryptService>();
builder.Services.AddScoped<ICommonFunctionalitiesService, CommonFunctionalitiesService>();
builder.Services.AddScoped<IExtensionMethodsService, ExtensionMethodsService>();
builder.Services.AddScoped<IHelpDeskCommonService, HelpDeskCommonService>();
builder.Services.AddScoped<IHelpDeskServiceRequestAPIService, HelpDeskServiceRequestAPIService>();
builder.Services.AddScoped<IUtilitiesService, UtilitiesService>();
builder.Services.AddScoped<IPartsCommonService, PartsCommonService>();


var app = builder.Build();

// Configure the HTTP request pipeline.
// if (app.Environment.IsDevelopment())
// {
//     app.UseSwagger();
//     app.UseSwaggerUI(c =>
//     {
//         c.SwaggerEndpoint("/swagger/v1/swagger.json", "PBC Core Service V1");
//         c.RoutePrefix = "swagger";
//     });
// }
app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "PBC Utility Service V1");
    c.RoutePrefix = "swagger";
});

app.UseHttpsRedirection();
app.UseCors("AllowAll");
app.UseAuthorization();

app.MapControllers();
app.MapHealthChecks("/health");

app.Run();

